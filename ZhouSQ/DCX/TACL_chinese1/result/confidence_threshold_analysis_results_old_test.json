[{"confidence_threshold": 0.0, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.01, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.02, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.03, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.04, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.05, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.06, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.07, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.08, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.09, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.1, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.11, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.12, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.13, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.14, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.15, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.16, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.17, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.18, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.19, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.2, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.21, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.22, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.23, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.24, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.25, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.26, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.27, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.28, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.29, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.3, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.31, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.32, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.33, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.34, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.35, "precision": 0.5933806146572104, "recall": 0.8394648829431438, "f1": 0.6952908587257618, "true_positives": 753, "false_positives": 516, "false_negatives": 144}, {"confidence_threshold": 0.36, "precision": 0.5947867298578199, "recall": 0.8394648829431438, "f1": 0.6962552011095701, "true_positives": 753, "false_positives": 513, "false_negatives": 144}, {"confidence_threshold": 0.37, "precision": 0.5985691573926868, "recall": 0.8394648829431438, "f1": 0.6988399071925754, "true_positives": 753, "false_positives": 505, "false_negatives": 144}, {"confidence_threshold": 0.38, "precision": 0.600480384307446, "recall": 0.8361204013377926, "f1": 0.6989748369058716, "true_positives": 750, "false_positives": 499, "false_negatives": 147}, {"confidence_threshold": 0.39, "precision": 0.6028938906752411, "recall": 0.8361204013377926, "f1": 0.7006071929005138, "true_positives": 750, "false_positives": 494, "false_negatives": 147}, {"confidence_threshold": 0.4, "precision": 0.6112469437652812, "recall": 0.8361204013377926, "f1": 0.7062146892655368, "true_positives": 750, "false_positives": 477, "false_negatives": 147}, {"confidence_threshold": 0.41, "precision": 0.6238532110091743, "recall": 0.8338907469342252, "f1": 0.7137404580152671, "true_positives": 748, "false_positives": 451, "false_negatives": 149}, {"confidence_threshold": 0.42, "precision": 0.6330508474576271, "recall": 0.8327759197324415, "f1": 0.7193066923447279, "true_positives": 747, "false_positives": 433, "false_negatives": 150}, {"confidence_threshold": 0.43, "precision": 0.6462882096069869, "recall": 0.8249721293199554, "f1": 0.7247796278158668, "true_positives": 740, "false_positives": 405, "false_negatives": 157}, {"confidence_threshold": 0.44, "precision": 0.6600896860986547, "recall": 0.8205128205128205, "f1": 0.7316103379721669, "true_positives": 736, "false_positives": 379, "false_negatives": 161}, {"confidence_threshold": 0.45, "precision": 0.6790009250693803, "recall": 0.8182831661092531, "f1": 0.7421638018200203, "true_positives": 734, "false_positives": 347, "false_negatives": 163}, {"confidence_threshold": 0.46, "precision": 0.6931067044381491, "recall": 0.8182831661092531, "f1": 0.7505112474437627, "true_positives": 734, "false_positives": 325, "false_negatives": 163}, {"confidence_threshold": 0.47, "precision": 0.705078125, "recall": 0.8049052396878483, "f1": 0.7516918271733471, "true_positives": 722, "false_positives": 302, "false_negatives": 175}, {"confidence_threshold": 0.48, "precision": 0.7151394422310757, "recall": 0.8004459308807135, "f1": 0.7553918990005261, "true_positives": 718, "false_positives": 286, "false_negatives": 179}, {"confidence_threshold": 0.49, "precision": 0.723404255319149, "recall": 0.7959866220735786, "f1": 0.7579617834394905, "true_positives": 714, "false_positives": 273, "false_negatives": 183}, {"confidence_threshold": 0.5, "precision": 0.7359081419624217, "recall": 0.7859531772575251, "f1": 0.7601078167115903, "true_positives": 705, "false_positives": 253, "false_negatives": 192}, {"confidence_threshold": 0.51, "precision": 0.750271444082519, "recall": 0.770345596432553, "f1": 0.7601760176017602, "true_positives": 691, "false_positives": 230, "false_negatives": 206}, {"confidence_threshold": 0.52, "precision": 0.7607709750566893, "recall": 0.7480490523968785, "f1": 0.7543563799887576, "true_positives": 671, "false_positives": 211, "false_negatives": 226}, {"confidence_threshold": 0.53, "precision": 0.7726737338044759, "recall": 0.7313266443701226, "f1": 0.7514318442153494, "true_positives": 656, "false_positives": 193, "false_negatives": 241}, {"confidence_threshold": 0.54, "precision": 0.7894736842105263, "recall": 0.7023411371237458, "f1": 0.7433628318584071, "true_positives": 630, "false_positives": 168, "false_negatives": 267}, {"confidence_threshold": 0.55, "precision": 0.7973684210526316, "recall": 0.6755852842809364, "f1": 0.7314423657211829, "true_positives": 606, "false_positives": 154, "false_negatives": 291}, {"confidence_threshold": 0.56, "precision": 0.8145604395604396, "recall": 0.6610925306577481, "f1": 0.7298461538461538, "true_positives": 593, "false_positives": 135, "false_negatives": 304}, {"confidence_threshold": 0.57, "precision": 0.8258992805755395, "recall": 0.6399108138238573, "f1": 0.721105527638191, "true_positives": 574, "false_positives": 121, "false_negatives": 323}, {"confidence_threshold": 0.58, "precision": 0.8335854765506808, "recall": 0.6142697881828316, "f1": 0.7073170731707316, "true_positives": 551, "false_positives": 110, "false_negatives": 346}, {"confidence_threshold": 0.59, "precision": 0.8539325842696629, "recall": 0.5930880713489409, "f1": 0.7000000000000001, "true_positives": 532, "false_positives": 91, "false_negatives": 365}, {"confidence_threshold": 0.6, "precision": 0.8672566371681416, "recall": 0.5462653288740246, "f1": 0.6703146374829002, "true_positives": 490, "false_positives": 75, "false_negatives": 407}, {"confidence_threshold": 0.61, "precision": 0.8838095238095238, "recall": 0.5172798216276477, "f1": 0.6526019690576653, "true_positives": 464, "false_positives": 61, "false_negatives": 433}, {"confidence_threshold": 0.62, "precision": 0.8963414634146342, "recall": 0.4916387959866221, "f1": 0.6349892008639308, "true_positives": 441, "false_positives": 51, "false_negatives": 456}, {"confidence_threshold": 0.63, "precision": 0.9113082039911308, "recall": 0.45819397993311034, "f1": 0.6097922848664689, "true_positives": 411, "false_positives": 40, "false_negatives": 486}, {"confidence_threshold": 0.64, "precision": 0.9166666666666666, "recall": 0.4169453734671126, "f1": 0.5731800766283525, "true_positives": 374, "false_positives": 34, "false_negatives": 523}, {"confidence_threshold": 0.65, "precision": 0.9285714285714286, "recall": 0.37681159420289856, "f1": 0.5360824742268041, "true_positives": 338, "false_positives": 26, "false_negatives": 559}, {"confidence_threshold": 0.66, "precision": 0.9260450160771704, "recall": 0.3210702341137124, "f1": 0.4768211920529802, "true_positives": 288, "false_positives": 23, "false_negatives": 609}, {"confidence_threshold": 0.67, "precision": 0.9509433962264151, "recall": 0.2809364548494983, "f1": 0.43373493975903615, "true_positives": 252, "false_positives": 13, "false_negatives": 645}, {"confidence_threshold": 0.68, "precision": 0.9502262443438914, "recall": 0.23411371237458195, "f1": 0.3756708407871199, "true_positives": 210, "false_positives": 11, "false_negatives": 687}, {"confidence_threshold": 0.69, "precision": 0.9666666666666667, "recall": 0.1939799331103679, "f1": 0.3231197771587744, "true_positives": 174, "false_positives": 6, "false_negatives": 723}, {"confidence_threshold": 0.7, "precision": 0.9642857142857143, "recall": 0.1505016722408027, "f1": 0.2603664416586307, "true_positives": 135, "false_positives": 5, "false_negatives": 762}, {"confidence_threshold": 0.71, "precision": 0.9682539682539683, "recall": 0.13600891861761427, "f1": 0.23851417399804498, "true_positives": 122, "false_positives": 4, "false_negatives": 775}, {"confidence_threshold": 0.72, "precision": 0.9555555555555556, "recall": 0.09587513935340022, "f1": 0.17426545086119555, "true_positives": 86, "false_positives": 4, "false_negatives": 811}, {"confidence_threshold": 0.73, "precision": 0.9850746268656716, "recall": 0.07357859531772576, "f1": 0.13692946058091288, "true_positives": 66, "false_positives": 1, "false_negatives": 831}, {"confidence_threshold": 0.74, "precision": 0.9824561403508771, "recall": 0.06243032329988852, "f1": 0.11740041928721173, "true_positives": 56, "false_positives": 1, "false_negatives": 841}, {"confidence_threshold": 0.75, "precision": 0.9705882352941176, "recall": 0.03678929765886288, "f1": 0.07089151450053706, "true_positives": 33, "false_positives": 1, "false_negatives": 864}, {"confidence_threshold": 0.76, "precision": 0.9565217391304348, "recall": 0.024526198439241916, "f1": 0.047826086956521734, "true_positives": 22, "false_positives": 1, "false_negatives": 875}, {"confidence_threshold": 0.77, "precision": 0.9, "recall": 0.010033444816053512, "f1": 0.019845644983461964, "true_positives": 9, "false_positives": 1, "false_negatives": 888}, {"confidence_threshold": 0.78, "precision": 0.875, "recall": 0.007803790412486065, "f1": 0.015469613259668509, "true_positives": 7, "false_positives": 1, "false_negatives": 890}, {"confidence_threshold": 0.79, "precision": 0.8333333333333334, "recall": 0.005574136008918618, "f1": 0.011074197120708748, "true_positives": 5, "false_positives": 1, "false_negatives": 892}, {"confidence_threshold": 0.8, "precision": 1.0, "recall": 0.0033444816053511705, "f1": 0.006666666666666666, "true_positives": 3, "false_positives": 0, "false_negatives": 894}, {"confidence_threshold": 0.81, "precision": 1.0, "recall": 0.0011148272017837235, "f1": 0.0022271714922048997, "true_positives": 1, "false_positives": 0, "false_negatives": 896}, {"confidence_threshold": 0.82, "precision": 1.0, "recall": 0.0011148272017837235, "f1": 0.0022271714922048997, "true_positives": 1, "false_positives": 0, "false_negatives": 896}, {"confidence_threshold": 0.83, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.84, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.85, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.86, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.87, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.88, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.89, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.9, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.91, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.92, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.93, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.94, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.95, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.96, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.97, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.98, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 0.99, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.0, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.01, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.02, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.03, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}, {"confidence_threshold": 1.04, "precision": 0, "recall": 0.0, "f1": 0, "true_positives": 0, "false_positives": 0, "false_negatives": 897}]